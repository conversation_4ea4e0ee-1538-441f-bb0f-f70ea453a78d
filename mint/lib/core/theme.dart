import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mental_health/core/extension/extensions.dart';

class FontSizes {
  static const double size10 = 10.0;
  static const double size11 = 11.0;
  static const double size12 = 12.0;
  static const double size13 = 13.0;
  static const double size14 = 14.0;
  static const double size15 = 15.0;
  static const double size16 = 16.0;
  static const double size17 = 17.0;
  static const double size18 = 18.0;
  static const double size19 = 19.0;
  static const double size20 = 20.0;
  static const double size21 = 21.0;
  static const double size22 = 22.0;
  static const double size23 = 23.0;
  static const double size24 = 24.0;
  static const double size25 = 25.0;
  static const double size26 = 26.0;
  static const double size27 = 27.0;
  static const double size28 = 28.0;
  static const double size29 = 29.0;
  static const double size30 = 30.0;
  static const double size31 = 31.0;
  static const double size32 = 32.0;
  static const double size33 = 33.0;
  static const double size34 = 34.0;
  static const double size35 = 35.0;
  static const double size36 = 36.0;
  static const double size37 = 37.0;
  static const double size38 = 38.0;
  static const double size39 = 39.0;
  static const double size40 = 40.0;
  static const double size41 = 41.0;
  static const double size42 = 42.0;
  static const double size43 = 43.0;
  static const double size44 = 44.0;
  static const double size45 = 45.0;
  static const double size46 = 46.0;
  static const double size47 = 47.0;
  static const double size48 = 48.0;
}

class DefaultColors {
  static Color get textPrimary => DateTime.now().isMorning
      ? const Color(0xFF665F73)
      : const Color(0xFFFFFFFF);

  static Color get reverseTextPrimary => DateTime.now().isMorning
      ? const Color(0xFFFFFFFF)
      : const Color(0xFF665F73);

  static Color get primary => const Color(0xFF98ba82);

  static Color get reversePrimary => DateTime.now().isMorning
      ? const Color(0xFF121B31)
      : const Color(0xFF98ba82);

  static const Color secondary = Color(0xFF371B34);

  static const Color white = Color(0xFFFFFFFF);
  static const Color pink = Color(0xFFEA2B83);
  static const Color lightpink = Color(0xFFFCDDEC);
  static const Color purple = Color(0xFF8E8FF8);
  static const Color orange = Color(0xFFF18F3B);
  static const Color lightteal = Color(0xFF58D5D4);
  static const Color darksea = Color(0xFF121B31);
  static const Color tea = Color(0xFF98ba82); // Màu trà xanh nhạt cho buổi sáng

  // Bộ màu mới nổi bật trên nền trắng
  static const Color dopamine = Color(0xFFE67E22); // Cam đậm cho mục tiêu
  static const Color dopamineNeon = Color(0xFFFF6B35); // Cam neon sáng
  static const Color endorphin =
      Color(0xFF3498DB); // Xanh dương đậm cho vận động
  static const Color endorphinNeon = Color(0xFF5DADE2); // Xanh dương neon sáng
  static const Color oxytocin = Color(0xFFE91E63); // Hồng đậm cho gắn kết
  static const Color oxytocinNeon = Color(0xFFFF4081); // Hồng neon sáng
  static const Color serotonin = Color(0xFF27AE60); // Xanh lá đậm cho tự tin
  static const Color serotoninNeon = Color(0xFF2ECC71); // Xanh lá neon sáng

  static Color task1 = Colors.pink[100]!;
  static Color task2 = Colors.orange[100]!;
  static Color task3 = Colors.green[100]!;
}

class TextPresets {
  // Small labels & captions
  static const TextStyle labelSmall = TextStyle(
    fontSize: FontSizes.size10,
    fontWeight: FontWeight.w400,
    height: 1.2,
  );

  static const TextStyle label = TextStyle(
    fontSize: FontSizes.size12,
    fontWeight: FontWeight.w500,
    height: 1.3,
  );

  static const TextStyle labelLarge = TextStyle(
    fontSize: FontSizes.size14,
    fontWeight: FontWeight.w600,
    height: 1.3,
  );

  // Body Text
  static const TextStyle bodySmall = TextStyle(
    fontSize: FontSizes.size13,
    fontWeight: FontWeight.w400,
    height: 1.4,
  );

  static const TextStyle body = TextStyle(
    fontSize: FontSizes.size14,
    fontWeight: FontWeight.w400,
    height: 1.5,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontSize: FontSizes.size16,
    fontWeight: FontWeight.w400,
    height: 1.5,
  );

  static const TextStyle bodyLarge = TextStyle(
    fontSize: FontSizes.size18,
    fontWeight: FontWeight.w500,
    height: 1.5,
  );

  // Titles
  static const TextStyle titleSmall = TextStyle(
    fontSize: FontSizes.size20,
    fontWeight: FontWeight.w500,
    height: 1.4,
  );

  static const TextStyle title = TextStyle(
    fontSize: FontSizes.size22,
    fontWeight: FontWeight.w600,
    height: 1.4,
  );

  static const TextStyle titleLarge = TextStyle(
    fontSize: FontSizes.size24,
    fontWeight: FontWeight.w600,
    height: 1.4,
  );

  // Headlines
  static const TextStyle headlineSmall = TextStyle(
    fontSize: FontSizes.size28,
    fontWeight: FontWeight.w600,
    height: 1.3,
  );

  static const TextStyle headline = TextStyle(
    fontSize: FontSizes.size32,
    fontWeight: FontWeight.w700,
    height: 1.25,
  );

  static const TextStyle headlineLarge = TextStyle(
    fontSize: FontSizes.size36,
    fontWeight: FontWeight.w700,
    height: 1.2,
  );

  // Display / Hero
  static const TextStyle displaySmall = TextStyle(
    fontSize: FontSizes.size40,
    fontWeight: FontWeight.w800,
    height: 1.1,
  );

  static const TextStyle display = TextStyle(
    fontSize: FontSizes.size44,
    fontWeight: FontWeight.w800,
    height: 1.1,
  );

  static const TextStyle displayLarge = TextStyle(
    fontSize: FontSizes.size48,
    fontWeight: FontWeight.w900,
    height: 1.05,
  );

  static const TextStyle price = TextStyle(
    fontSize: FontSizes.size16,
    fontWeight: FontWeight.w900,
    height: 1.5,
  );
}

class AppTheme {
  static ThemeData get lightTheme {
    //getter lighTheme
    return ThemeData(
      primaryColor: const Color(0xFFAEAFF7),
      focusColor: const Color(0xFF371B34),
      textTheme: TextTheme(
        bodySmall: GoogleFonts.quicksand(
          fontSize: FontSizes.size10,
          color: Colors.white,
        ),
        bodyMedium: GoogleFonts.quicksand(
          fontSize: FontSizes.size10,
          color: Colors.white,
        ),
        bodyLarge: GoogleFonts.quicksand(
          fontSize: FontSizes.size10,
          color: Colors.white,
        ),
        titleSmall: GoogleFonts.quicksand(
          fontSize: FontSizes.size10,
          color: Colors.black,
        ),
        titleMedium: GoogleFonts.quicksand(
          fontSize: FontSizes.size10,
          color: Colors.black,
        ),
        titleLarge: GoogleFonts.quicksand(
            fontSize: FontSizes.size10,
            color: Colors.black,
            fontWeight: FontWeight.bold),
        labelSmall: GoogleFonts.quicksand(
          fontSize: FontSizes.size10,
          color: Colors.black,
        ),
        labelMedium: GoogleFonts.quicksand(
          fontSize: FontSizes.size10,
          color: Colors.black,
        ),
        labelLarge: GoogleFonts.quicksand(
            fontSize: FontSizes.size10,
            color: Colors.black,
            fontWeight: FontWeight.bold),
      ),
    );
  }
}
